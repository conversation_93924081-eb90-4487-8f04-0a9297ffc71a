@page "/orders"
@page "/orders/{OrderId}"

<h1 class="visually-hidden">Gestion des Commandes - NafaPlace</h1>

<div class="container-fluid px-4">
    <h1 class="mt-4">Gestion des Commandes</h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item"><a href="/dashboard">Dashboard</a></li>
        <li class="breadcrumb-item active">Commandes</li>
    </ol>

    @if (!string.IsNullOrEmpty(OrderId))
    {
        <div class="card mb-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <div>
                    <i class="fas fa-file-invoice me-1"></i>
                    Détails de la Commande #@OrderId
                </div>
                <button class="btn btn-outline-secondary" @onclick="BackToOrdersList">
                    <i class="fas fa-arrow-left me-1"></i> Retour à la liste
                </button>
            </div>
            <div class="card-body">
                <div class="row mb-4">
                    <div class="col-md-6">
                        <h5>Informations Client</h5>
                        <div class="mb-2"><strong>Nom:</strong> @_currentOrder.CustomerName</div>
                        <div class="mb-2"><strong>Email:</strong> @_currentOrder.CustomerEmail</div>
                        <div class="mb-2"><strong>Téléphone:</strong> @_currentOrder.CustomerPhone</div>
                        <div class="mb-2"><strong>Date de commande:</strong> @_currentOrder.OrderDate.ToString("dd/MM/yyyy HH:mm")</div>
                    </div>
                    <div class="col-md-6">
                        <h5>Informations Livraison</h5>
                        <div class="mb-2"><strong>Adresse:</strong> @_currentOrder.ShippingAddress</div>
                        <div class="mb-2"><strong>Ville:</strong> @_currentOrder.ShippingCity</div>
                        <div class="mb-2"><strong>Méthode de livraison:</strong> @_currentOrder.ShippingMethod</div>
                        <div class="mb-2">
                            <strong>Statut:</strong>
                            <select class="form-select form-select-sm d-inline-block w-auto ms-2" @bind="_currentOrder.Status">
                                <option value="En attente">En attente</option>
                                <option value="Confirmé">Confirmé</option>
                                <option value="En préparation">En préparation</option>
                                <option value="Expédié">Expédié</option>
                                <option value="Livré">Livré</option>
                                <option value="Annulé">Annulé</option>
                            </select>
                            <button class="btn btn-sm btn-primary ms-2" @onclick="UpdateOrderStatus">Mettre à jour</button>
                        </div>
                    </div>
                </div>

                <h5>Produits Commandés</h5>
                <table class="table table-bordered table-striped">
                    <thead>
                        <tr>
                            <th style="width: 80px;">Image</th>
                            <th>Produit</th>
                            <th>Prix unitaire</th>
                            <th>Quantité</th>
                            <th>Total</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var item in _currentOrder.Items)
                        {
                            <tr>
                                <td>
                                    <img src="@item.ProductImageUrl" alt="@item.ProductName" class="img-thumbnail" style="width: 60px; height: 60px; object-fit: cover;" />
                                </td>
                                <td>@item.ProductName</td>
                                <td>@item.UnitPrice XOF</td>
                                <td>@item.Quantity</td>
                                <td>@(item.UnitPrice * item.Quantity) XOF</td>
                            </tr>
                        }
                    </tbody>
                    <tfoot>
                        <tr>
                            <td colspan="4" class="text-end"><strong>Sous-total:</strong></td>
                            <td>@_currentOrder.Subtotal XOF</td>
                        </tr>
                        <tr>
                            <td colspan="4" class="text-end"><strong>Frais de livraison:</strong></td>
                            <td>@_currentOrder.ShippingFee XOF</td>
                        </tr>
                        <tr>
                            <td colspan="4" class="text-end"><strong>Total:</strong></td>
                            <td><strong>@_currentOrder.TotalAmount XOF</strong></td>
                        </tr>
                    </tfoot>
                </table>

                <div class="row mt-4">
                    <div class="col-md-6">
                        <h5>Paiement</h5>
                        <div class="mb-2"><strong>Méthode:</strong> @_currentOrder.PaymentMethod</div>
                        <div class="mb-2"><strong>Statut:</strong> @_currentOrder.PaymentStatus</div>
                        @if (_currentOrder.PaymentStatus != "Payé")
                        {
                            <button class="btn btn-success" @onclick="MarkAsPaid">Marquer comme payé</button>
                        }
                    </div>
                    <div class="col-md-6">
                        <h5>Notes</h5>
                        <textarea class="form-control" rows="3" @bind="_currentOrder.Notes"></textarea>
                        <button class="btn btn-primary mt-2" @onclick="SaveNotes">Enregistrer les notes</button>
                    </div>
                </div>
            </div>
        </div>
    }
    else
    {
        <div class="card mb-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <div>
                    <i class="fas fa-table me-1"></i>
                    Liste des Commandes
                </div>
                <div>
                    <button class="btn btn-outline-primary me-2" @onclick="ExportOrders">
                        <i class="fas fa-file-export me-1"></i> Exporter
                    </button>
                    <button class="btn btn-outline-secondary" @onclick="RefreshOrders">
                        <i class="fas fa-sync me-1"></i> Actualiser
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-md-4">
                        <div class="input-group">
                            <input type="text" class="form-control" placeholder="Rechercher..." @bind="_searchTerm" @bind:event="oninput">
                            <button class="btn btn-outline-secondary" type="button" @onclick="SearchOrders">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <select class="form-select" @bind="_selectedStatus">
                            <option value="">Tous les statuts</option>
                            <option value="En attente">En attente</option>
                            <option value="Confirmé">Confirmé</option>
                            <option value="En préparation">En préparation</option>
                            <option value="Expédié">Expédié</option>
                            <option value="Livré">Livré</option>
                            <option value="Annulé">Annulé</option>
                        </select>
                    </div>
                    <div class="col-md-5">
                        <div class="input-group">
                            <span class="input-group-text">Du</span>
                            <input type="date" class="form-control" @bind="_startDate">
                            <span class="input-group-text">Au</span>
                            <input type="date" class="form-control" @bind="_endDate">
                            <button class="btn btn-outline-secondary" type="button" @onclick="FilterByDate">
                                <i class="fas fa-filter"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <table class="table table-bordered table-hover">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Client</th>
                            <th>Date</th>
                            <th>Total</th>
                            <th>Statut</th>
                            <th>Paiement</th>
                            <th style="width: 120px;">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var order in _filteredOrders)
                        {
                            <tr>
                                <td>@order.Id</td>
                                <td>@order.CustomerName</td>
                                <td>@order.OrderDate.ToString("dd/MM/yyyy")</td>
                                <td>@order.TotalAmount XOF</td>
                                <td>
                                    <span class="badge @GetStatusBadgeClass(order.Status)">
                                        @order.Status
                                    </span>
                                </td>
                                <td>
                                    <span class="badge @GetPaymentStatusBadgeClass(order.PaymentStatus)">
                                        @order.PaymentStatus
                                    </span>
                                </td>
                                <td>
                                    <a href="/orders/@order.Id" class="btn btn-sm btn-primary me-1">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <button class="btn btn-sm btn-danger" @onclick="() => CancelOrder(order)">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>

                <nav aria-label="Page navigation">
                    <ul class="pagination justify-content-center">
                        <li class="page-item @(_currentPage == 1 ? "disabled" : "")">
                            <a class="page-link" href="javascript:void(0)" @onclick="PreviousPage">Précédent</a>
                        </li>
                        @for (int i = 1; i <= _totalPages; i++)
                        {
                            var pageNumber = i;
                            <li class="page-item @(pageNumber == _currentPage ? "active" : "")">
                                <a class="page-link" href="javascript:void(0)" @onclick="() => GoToPage(pageNumber)">@pageNumber</a>
                            </li>
                        }
                        <li class="page-item @(_currentPage == _totalPages ? "disabled" : "")">
                            <a class="page-link" href="javascript:void(0)" @onclick="NextPage">Suivant</a>
                        </li>
                    </ul>
                </nav>
            </div>
        </div>
    }
</div>

@code {
    [Parameter]
    public string? OrderId { get; set; }

    private List<Order> _orders = new List<Order>();
    private List<Order> _filteredOrders = new List<Order>();
    private Order _currentOrder = new Order();
    private string _searchTerm = "";
    private string _selectedStatus = "";
    private DateTime _startDate = DateTime.Now.AddDays(-30);
    private DateTime _endDate = DateTime.Now;
    private int _currentPage = 1;
    private int _pageSize = 10;
    private int _totalPages = 1;

    protected override void OnInitialized()
    {
        // Données de démonstration
        _orders = GenerateDemoOrders();
        FilterOrders();
    }

    protected override void OnParametersSet()
    {
        if (!string.IsNullOrEmpty(OrderId))
        {
            _currentOrder = _orders.FirstOrDefault(o => o.Id == OrderId) ?? new Order();
        }
    }

    private List<Order> GenerateDemoOrders()
    {
        var orders = new List<Order>
        {
            new Order
            {
                Id = "ORD-001",
                CustomerName = "Amadou Diallo",
                CustomerEmail = "<EMAIL>",
                CustomerPhone = "+224 621 12 34 56",
                OrderDate = DateTime.Now.AddDays(-1),
                ShippingAddress = "Quartier Almamya, Rue KA-020",
                ShippingCity = "Conakry",
                ShippingMethod = "Livraison standard",
                ShippingFee = 5000,
                PaymentMethod = "Mobile Money",
                PaymentStatus = "Payé",
                Status = "En attente",
                Subtotal = 45000,
                TotalAmount = 50000,
                Notes = "",
                Items = new List<OrderItem>
                {
                    new OrderItem { ProductName = "Smartphone XYZ", ProductImageUrl = "/images/products/smartphone.jpg", UnitPrice = 150000, Quantity = 1 }
                }
            },
            new Order
            {
                Id = "ORD-002",
                CustomerName = "Fatou Camara",
                CustomerEmail = "<EMAIL>",
                CustomerPhone = "+221 77 123 45 67",
                OrderDate = DateTime.Now.AddDays(-2),
                ShippingAddress = "Rue 10 x Avenue Blaise Diagne",
                ShippingCity = "Dakar",
                ShippingMethod = "Livraison express",
                ShippingFee = 8500,
                PaymentMethod = "Carte bancaire",
                PaymentStatus = "Payé",
                Status = "Expédié",
                Subtotal = 70000,
                TotalAmount = 78500,
                Notes = "Client fidèle, livraison prioritaire",
                Items = new List<OrderItem>
                {
                    new OrderItem { ProductName = "T-shirt Coton", ProductImageUrl = "/images/products/tshirt.jpg", UnitPrice = 8000, Quantity = 2 },
                    new OrderItem { ProductName = "Robe d'Été", ProductImageUrl = "/images/products/dress.jpg", UnitPrice = 15000, Quantity = 1 },
                    new OrderItem { ProductName = "Écouteurs Sans Fil", ProductImageUrl = "/images/products/headphones.jpg", UnitPrice = 25000, Quantity = 1 }
                }
            },
            new Order
            {
                Id = "ORD-003",
                CustomerName = "Moussa Traoré",
                CustomerEmail = "<EMAIL>",
                CustomerPhone = "+223 66 789 01 23",
                OrderDate = DateTime.Now.AddDays(-3),
                ShippingAddress = "Quartier du Fleuve, Rue 311, Porte 20",
                ShippingCity = "Bamako",
                ShippingMethod = "Livraison standard",
                ShippingFee = 5000,
                PaymentMethod = "Paiement à la livraison",
                PaymentStatus = "En attente",
                Status = "Confirmé",
                Subtotal = 120000,
                TotalAmount = 125000,
                Notes = "Appeler avant livraison",
                Items = new List<OrderItem>
                {
                    new OrderItem { ProductName = "Tablette 10 pouces", ProductImageUrl = "/images/products/tablet.jpg", UnitPrice = 120000, Quantity = 1 }
                }
            },
            new Order
            {
                Id = "ORD-004",
                CustomerName = "Aïcha Bah",
                CustomerEmail = "<EMAIL>",
                CustomerPhone = "+224 666 55 44 33",
                OrderDate = DateTime.Now.AddDays(-3),
                ShippingAddress = "Quartier Kipé, Immeuble Bleu",
                ShippingCity = "Conakry",
                ShippingMethod = "Livraison standard",
                ShippingFee = 5000,
                PaymentMethod = "Mobile Money",
                PaymentStatus = "Payé",
                Status = "En attente",
                Subtotal = 30000,
                TotalAmount = 35000,
                Notes = "",
                Items = new List<OrderItem>
                {
                    new OrderItem { ProductName = "Crème Hydratante", ProductImageUrl = "/images/products/cream.jpg", UnitPrice = 7500, Quantity = 2 },
                    new OrderItem { ProductName = "Huile Essentielle", ProductImageUrl = "/images/products/oil.jpg", UnitPrice = 6500, Quantity = 1 },
                    new OrderItem { ProductName = "Thé Vert Bio", ProductImageUrl = "/images/products/tea.jpg", UnitPrice = 3000, Quantity = 3 }
                }
            },
            new Order
            {
                Id = "ORD-005",
                CustomerName = "Ibrahim Sow",
                CustomerEmail = "<EMAIL>",
                CustomerPhone = "+225 07 12 34 56",
                OrderDate = DateTime.Now.AddDays(-4),
                ShippingAddress = "Cocody Riviera 3, Rue des Jardins",
                ShippingCity = "Abidjan",
                ShippingMethod = "Livraison express",
                ShippingFee = 7000,
                PaymentMethod = "Carte bancaire",
                PaymentStatus = "Remboursé",
                Status = "Annulé",
                Subtotal = 85000,
                TotalAmount = 92000,
                Notes = "Commande annulée à la demande du client",
                Items = new List<OrderItem>
                {
                    new OrderItem { ProductName = "Lampe de Bureau", ProductImageUrl = "/images/products/lamp.jpg", UnitPrice = 12000, Quantity = 1 },
                    new OrderItem { ProductName = "Coussin Décoratif", ProductImageUrl = "/images/products/pillow.jpg", UnitPrice = 9000, Quantity = 3 },
                    new OrderItem { ProductName = "Chemise Homme", ProductImageUrl = "/images/products/shirt.jpg", UnitPrice = 12000, Quantity = 3 }
                }
            }
        };

        // Ajouter plus de commandes pour la pagination
        for (int i = 6; i <= 25; i++)
        {
            var order = new Order
            {
                Id = $"ORD-{i:000}",
                CustomerName = $"Client Test {i}",
                CustomerEmail = $"client{i}@example.com",
                CustomerPhone = "+224 666 00 00 00",
                OrderDate = DateTime.Now.AddDays(-i),
                ShippingAddress = "Adresse de test",
                ShippingCity = "Ville Test",
                ShippingMethod = "Livraison standard",
                ShippingFee = 5000,
                PaymentMethod = i % 2 == 0 ? "Mobile Money" : "Paiement à la livraison",
                PaymentStatus = i % 2 == 0 ? "Payé" : "En attente",
                Status = i % 5 == 0 ? "Livré" : (i % 4 == 0 ? "Expédié" : (i % 3 == 0 ? "En préparation" : (i % 2 == 0 ? "Confirmé" : "En attente"))),
                Subtotal = 20000 * (i % 3 + 1),
                TotalAmount = 20000 * (i % 3 + 1) + 5000,
                Notes = "",
                Items = new List<OrderItem>
                {
                    new OrderItem { ProductName = "Produit Test", ProductImageUrl = GetDefaultImageUrl(), UnitPrice = 20000, Quantity = i % 3 + 1 }
                }
            };
            orders.Add(order);
        }

        return orders;
    }

    private void FilterOrders()
    {
        var query = _orders.AsQueryable();

        if (!string.IsNullOrEmpty(_searchTerm))
        {
            query = query.Where(o => 
                o.Id.Contains(_searchTerm, StringComparison.OrdinalIgnoreCase) || 
                o.CustomerName.Contains(_searchTerm, StringComparison.OrdinalIgnoreCase) || 
                o.CustomerEmail.Contains(_searchTerm, StringComparison.OrdinalIgnoreCase)
            );
        }

        if (!string.IsNullOrEmpty(_selectedStatus))
        {
            query = query.Where(o => o.Status == _selectedStatus);
        }

        query = query.Where(o => o.OrderDate.Date >= _startDate.Date && o.OrderDate.Date <= _endDate.Date);

        var filteredList = query.OrderByDescending(o => o.OrderDate).ToList();
        _totalPages = (int)Math.Ceiling(filteredList.Count / (double)_pageSize);
        
        if (_currentPage > _totalPages && _totalPages > 0)
        {
            _currentPage = _totalPages;
        }

        _filteredOrders = filteredList
            .Skip((_currentPage - 1) * _pageSize)
            .Take(_pageSize)
            .ToList();
    }

    private void SearchOrders()
    {
        _currentPage = 1;
        FilterOrders();
    }

    private void FilterByDate()
    {
        _currentPage = 1;
        FilterOrders();
    }

    private void PreviousPage()
    {
        if (_currentPage > 1)
        {
            _currentPage--;
            FilterOrders();
        }
    }

    private void NextPage()
    {
        if (_currentPage < _totalPages)
        {
            _currentPage++;
            FilterOrders();
        }
    }

    private void GoToPage(int page)
    {
        if (page >= 1 && page <= _totalPages)
        {
            _currentPage = page;
            FilterOrders();
        }
    }

    private string GetStatusBadgeClass(string status)
    {
        return status switch
        {
            "En attente" => "bg-warning",
            "Confirmé" => "bg-info",
            "En préparation" => "bg-primary",
            "Expédié" => "bg-info",
            "Livré" => "bg-success",
            "Annulé" => "bg-danger",
            _ => "bg-secondary"
        };
    }

    private string GetPaymentStatusBadgeClass(string status)
    {
        return status switch
        {
            "Payé" => "bg-success",
            "En attente" => "bg-warning",
            "Remboursé" => "bg-info",
            "Échoué" => "bg-danger",
            _ => "bg-secondary"
        };
    }

    private void CancelOrder(Order order)
    {
        if (order.Status != "Livré" && order.Status != "Annulé")
        {
            order.Status = "Annulé";
            FilterOrders();
        }
    }

    private void RefreshOrders()
    {
        FilterOrders();
    }

    private void ExportOrders()
    {
        // Implémenter l'exportation des commandes
        Console.WriteLine("Exporting orders...");
    }

    private void BackToOrdersList()
    {
        OrderId = null;
    }

    private void UpdateOrderStatus()
    {
        var order = _orders.FirstOrDefault(o => o.Id == OrderId);
        if (order != null)
        {
            order.Status = _currentOrder.Status;
        }
    }

    private void MarkAsPaid()
    {
        var order = _orders.FirstOrDefault(o => o.Id == OrderId);
        if (order != null)
        {
            order.PaymentStatus = "Payé";
            _currentOrder.PaymentStatus = "Payé";
        }
    }

    private void SaveNotes()
    {
        var order = _orders.FirstOrDefault(o => o.Id == OrderId);
        if (order != null)
        {
            order.Notes = _currentOrder.Notes;
        }
    }

    private string GetDefaultImageUrl()
    {
        // Image par défaut en base64 (petit carré gris)
        return "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAGQAAABkCAYAAABw4pVUAAAACXBIWXMAAAsTAAALEwEAmpwYAAAF8WlUWHRYTUw6Y29tLmFkb2JlLnhtcAAAAAAAPD94cGFja2V0IGJlZ2luPSLvu78iIGlkPSJXNU0wTXBDZWhpSHpyZVN6TlRjemtjOWQiPz4gPHg6eG1wbWV0YSB4bWxuczp4PSJhZG9iZTpuczptZXRhLyIgeDp4bXB0az0iQWRvYmUgWE1QIENvcmUgNS42LWMxNDUgNzkuMTYzNDk5LCAyMDE4LzA4LzEzLTE2OjQwOjIyICAgICAgICAiPiA8cmRmOlJERiB4bWxuczpyZGY9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkvMDIvMjItcmRmLXN5bnRheC1ucyMiPiA8cmRmOkRlc2NyaXB0aW9uIHJkZjphYm91dD0iIiB4bWxuczp4bXA9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC8iIHhtbG5zOmRjPSJodHRwOi8vcHVybC5vcmcvZGMvZWxlbWVudHMvMS4xLyIgeG1sbnM6cGhvdG9zaG9wPSJodHRwOi8vbnMuYWRvYmUuY29tL3Bob3Rvc2hvcC8xLjAvIiB4bWxuczp4bXBNTT0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL21tLyIgeG1sbnM6c3RFdnQ9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9zVHlwZS9SZXNvdXJjZUV2ZW50IyIgeG1wOkNyZWF0b3JUb29sPSJBZG9iZSBQaG90b3Nob3AgQ0MgMjAxOSAoTWFjaW50b3NoKSIgeG1wOkNyZWF0ZURhdGU9IjIwMjUtMDMtMjFUMjI6MjA6NDIrMDE6MDAiIHhtcDpNb2RpZnlEYXRlPSIyMDI1LTAzLTIxVDIyOjIwOjQyKzAxOjAwIiB4bXA6TWV0YWRhdGFEYXRlPSIyMDI1LTAzLTIxVDIyOjIwOjQyKzAxOjAwIiBkYzpmb3JtYXQ9ImltYWdlL3BuZyIgcGhvdG9zaG9wOkNvbG9yTW9kZT0iMyIgcGhvdG9zaG9wOklDQ1Byb2ZpbGU9InNSR0IgSUVDNjE5NjYtMi4xIiB4bXBNTTpJbnN0YW5jZUlEPSJ4bXAuaWlkOmIyYzVkYTFmLWRlZGQtNDZkMS05ZmQ2LTZlMjZhMDQyZTM5YyIgeG1wTU06RG9jdW1lbnRJRD0ieG1wLmRpZDpiMmM1ZGExZi1kZWRkLTQ2ZDEtOWZkNi02ZTI2YTA0MmUzOWMiIHhtcE1NOk9yaWdpbmFsRG9jdW1lbnRJRD0ieG1wLmRpZDpiMmM1ZGExZi1kZWRkLTQ2ZDEtOWZkNi02ZTI2YTA0MmUzOWMiPiA8eG1wTU06SGlzdG9yeT4gPHJkZjpTZXE+IDxyZGY6bGkgc3RFdnQ6YWN0aW9uPSJjcmVhdGVkIiBzdEV2dDppbnN0YW5jZUlEPSJ4bXAuaWlkOmIyYzVkYTFmLWRlZGQtNDZkMS05ZmQ2LTZlMjZhMDQyZTM5YyIgc3RFdnQ6d2hlbj0iMjAyNS0wMy0yMVQyMjoyMDo0MiswMTowMCIgc3RFdnQ6c29mdHdhcmVBZ2VudD0iQWRvYmUgUGhvdG9zaG9wIENDIDIwMTkgKE1hY2ludG9zaCkiLz4gPC9yZGY6U2VxPiA8L3htcE1NOkhpc3Rvcnk+IDwvcmRmOkRlc2NyaXB0aW9uPiA8L3JkZjpSREY+IDwveDp4bXBtZXRhPiA8P3hwYWNrZXQgZW5kPSJyIj8+LmMJwQAAAWJJREFUeJzt3EFqwzAUBFDR0kN06SN06yN16SOk9BANXRQCbkgXtqWZ/3hvb2xZMgzYWJYAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAMCHuqSbJI8kP0l+k9yTXFdOdU5dkntGfpLcklyWjXZOXZL7xj1j5rUTntE1yWPjnpnXTnhGXZLHzj1j5rUTntGW/8xrJzyjvXvGzGsnPKMuyW3nnjHz2gnP6JLxnLF1z5h57YRndMl4zti6Z8y8dsIz6pI8d+4ZM6+d8IyuGc8ZW/eMmddOeEbXjOeMrXvGzGsnPKMu43fGc+eeMfPaCc+oy/id8dy5Z8y8dsIzumX8znhu3DNmXjvhGd0yfmc8N+4ZM6+d8IxuGb8znhv3jJnXTnhGt4zfGc+Ne8bMayc8oy7jd8Zz454x89oJz6jL+J3x3LhnzLx2wjPqMn5nPDfuGTOvnRAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAgP/0B0XoHEY8zYJ0AAAAAElFTkSuQmCC";
    }

    public class Order
    {
        public string Id { get; set; } = "";
        public string CustomerName { get; set; } = "";
        public string CustomerEmail { get; set; } = "";
        public string CustomerPhone { get; set; } = "";
        public DateTime OrderDate { get; set; } = DateTime.Now;
        public string ShippingAddress { get; set; } = "";
        public string ShippingCity { get; set; } = "";
        public string ShippingMethod { get; set; } = "";
        public decimal ShippingFee { get; set; }
        public string PaymentMethod { get; set; } = "";
        public string PaymentStatus { get; set; } = "";
        public string Status { get; set; } = "";
        public decimal Subtotal { get; set; }
        public decimal TotalAmount { get; set; }
        public string Notes { get; set; } = "";
        public List<OrderItem> Items { get; set; } = new List<OrderItem>();
    }

    public class OrderItem
    {
        public string ProductName { get; set; } = "";
        public string ProductImageUrl { get; set; } = "";
        public decimal UnitPrice { get; set; }
        public int Quantity { get; set; }
    }
}
