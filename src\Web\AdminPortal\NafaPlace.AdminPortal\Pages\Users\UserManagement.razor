@page "/users"
@using Microsoft.AspNetCore.Components.Authorization
@using NafaPlace.AdminPortal.Models.Auth
@using UserModels = NafaPlace.AdminPortal.Models.Users
@using NafaPlace.AdminPortal.Services
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.Forms
@using Microsoft.AspNetCore.Components
@using Microsoft.AspNetCore.Components.Web
@using Microsoft.AspNetCore.Components.Routing
@using System.ComponentModel.DataAnnotations
@inject IAuthService AuthService
@inject IUserService UserService
@inject NavigationManager NavigationManager
@attribute [Authorize(Roles = "Admin")]

<PageTitle>Gestion des Utilisateurs - NafaPlace Admin</PageTitle>

<div class="container-fluid px-4">
    <h1 class="mt-4">Gestion des Utilisateurs</h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item"><a href="/dashboard">Tableau de bord</a></li>
        <li class="breadcrumb-item active">Utilisateurs</li>
    </ol>

    @if (!string.IsNullOrEmpty(errorMessage))
    {
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            @errorMessage
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    }

    @if (!string.IsNullOrEmpty(successMessage))
    {
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            @successMessage
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    }

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <div class="row align-items-center">
                        <div class="col">
                            <h5 class="card-title mb-0">Liste des Utilisateurs</h5>
                        </div>
                        <div class="col-auto">
                            <button class="btn btn-primary" @onclick="ShowCreateUserModal">
                                <i class="fas fa-plus"></i> Nouvel Utilisateur
                            </button>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Barre de recherche -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="input-group">
                                <input type="text" class="form-control" placeholder="Rechercher par nom, email..." 
                                       @bind="searchTerm" @onkeypress="OnSearchKeyPress" />
                                <button class="btn btn-outline-secondary" type="button" @onclick="SearchUsers">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="d-flex justify-content-end">
                                <select class="form-select w-auto" value="@pageSize" @onchange="OnPageSizeChanged">
                                    <option value="10">10 par page</option>
                                    <option value="20">20 par page</option>
                                    <option value="50">50 par page</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    @if (isLoading)
                    {
                        <div class="d-flex justify-content-center py-4">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Chargement...</span>
                            </div>
                        </div>
                    }
                    else if (users == null || !users.Any())
                    {
                        <div class="text-center py-4">
                            <i class="fas fa-users fa-3x text-muted mb-3"></i>
                            <p class="text-muted">Aucun utilisateur trouvé.</p>
                        </div>
                    }
                    else
                    {
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Nom complet</th>
                                        <th>Email</th>
                                        <th>Rôles</th>
                                        <th>Statut</th>
                                        <th>Date de création</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var user in users)
                                    {
                                        <tr>
                                            <td>@user.Id</td>
                                            <td>@user.FullName</td>
                                            <td>@user.Email</td>
                                            <td>
                                                @if (user.Roles.Any())
                                                {
                                                    @foreach (var role in user.Roles)
                                                    {
                                                        <span class="badge bg-primary me-1">@role</span>
                                                    }
                                                }
                                                else
                                                {
                                                    <span class="text-muted">Aucun rôle</span>
                                                }
                                            </td>
                                            <td>
                                                @if (user.IsActive)
                                                {
                                                    <span class="badge bg-success">Actif</span>
                                                }
                                                else
                                                {
                                                    <span class="badge bg-danger">Inactif</span>
                                                }
                                            </td>
                                            <td>@user.CreatedAt.ToShortDateString()</td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <button class="btn btn-sm btn-outline-primary" @onclick="() => EditUser(user)">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                    <button class="btn btn-sm btn-outline-info" @onclick="() => ManageUserRoles(user)">
                                                        <i class="fas fa-user-tag"></i>
                                                    </button>
                                                    <button class="btn btn-sm btn-outline-danger" @onclick="() => DeleteUserConfirmation(user)">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        @if (totalPages > 1)
                        {
                            <nav aria-label="Page navigation">
                                <ul class="pagination justify-content-center">
                                    <li class="page-item @(currentPage == 1 ? "disabled" : "")">
                                        <a class="page-link" href="javascript:void(0)" @onclick="PreviousPage">Précédent</a>
                                    </li>
                                    @for (int i = 1; i <= totalPages; i++)
                                    {
                                        var pageNumber = i;
                                        <li class="page-item @(pageNumber == currentPage ? "active" : "")">
                                            <a class="page-link" href="javascript:void(0)" @onclick="() => GoToPage(pageNumber)">@pageNumber</a>
                                        </li>
                                    }
                                    <li class="page-item @(currentPage == totalPages ? "disabled" : "")">
                                        <a class="page-link" href="javascript:void(0)" @onclick="NextPage">Suivant</a>
                                    </li>
                                </ul>
                            </nav>
                        }
                    }
                </div>
            </div>
        </div>
    </div>
</div>

@code {
    private List<UserModels.UserDto> users = new List<UserModels.UserDto>();
    private bool isLoading = true;
    private string errorMessage = string.Empty;
    private string successMessage = string.Empty;
    private string searchTerm = string.Empty;
    private int currentPage = 1;
    private int pageSize = 20;
    private int totalPages = 1;
    private int totalCount = 0;

    protected override async Task OnInitializedAsync()
    {
        await LoadUsers();
    }

    private async Task LoadUsers()
    {
        try
        {
            isLoading = true;
            errorMessage = string.Empty;
            successMessage = string.Empty;
            
            var result = await UserService.GetUsersAsync(currentPage, pageSize, searchTerm);
            users = result.Items.ToList();
            totalCount = result.TotalCount;
            totalPages = (int)Math.Ceiling((double)totalCount / pageSize);
        }
        catch (Exception ex)
        {
            errorMessage = $"Erreur lors du chargement des utilisateurs: {ex.Message}";
            Console.WriteLine(errorMessage);
        }
        finally
        {
            isLoading = false;
        }
    }

    private async Task SearchUsers()
    {
        currentPage = 1;
        await LoadUsers();
    }

    private async Task OnSearchKeyPress(KeyboardEventArgs e)
    {
        if (e.Key == "Enter")
        {
            await SearchUsers();
        }
    }

    private async Task OnPageSizeChanged(ChangeEventArgs e)
    {
        if (int.TryParse(e.Value?.ToString(), out var newPageSize))
        {
            pageSize = newPageSize;
            currentPage = 1;
            await LoadUsers();
        }
    }

    private async Task GoToPage(int page)
    {
        if (page >= 1 && page <= totalPages)
        {
            currentPage = page;
            await LoadUsers();
        }
    }

    private async Task PreviousPage()
    {
        if (currentPage > 1)
        {
            currentPage--;
            await LoadUsers();
        }
    }

    private async Task NextPage()
    {
        if (currentPage < totalPages)
        {
            currentPage++;
            await LoadUsers();
        }
    }

    private void ShowCreateUserModal()
    {
        // TODO: Implémenter la modal de création d'utilisateur
        successMessage = "Fonctionnalité de création d'utilisateur à implémenter";
    }

    private void EditUser(UserModels.UserDto user)
    {
        // TODO: Implémenter l'édition d'utilisateur
        successMessage = $"Édition de l'utilisateur {user.FullName} à implémenter";
    }

    private void ManageUserRoles(UserModels.UserDto user)
    {
        // TODO: Implémenter la gestion des rôles utilisateur
        successMessage = $"Gestion des rôles pour {user.FullName} à implémenter";
    }

    private void DeleteUserConfirmation(UserModels.UserDto user)
    {
        // TODO: Implémenter la confirmation de suppression
        successMessage = $"Suppression de l'utilisateur {user.FullName} à implémenter";
    }
}
