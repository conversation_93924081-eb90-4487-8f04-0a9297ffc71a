using System.Net.Http.Headers;
using System.Text;
using System.Text.Json;
using Microsoft.JSInterop;
using NafaPlace.SellerPortal.Models.Statistics;

namespace NafaPlace.SellerPortal.Services;

public class StatisticsService : IStatisticsService
{
    private readonly IHttpClientFactory _httpClientFactory;
    private readonly IJSRuntime _jsRuntime;
    private readonly IConfiguration _configuration;

    public StatisticsService(IHttpClientFactory httpClientFactory, IJSRuntime jsRuntime, IConfiguration configuration)
    {
        _httpClientFactory = httpClientFactory;
        _jsRuntime = jsRuntime;
        _configuration = configuration;
    }

    public async Task<DashboardStatistics> GetDashboardStatisticsAsync(StatisticsRequest request)
    {
        try
        {
            using var httpClient = _httpClientFactory.CreateClient();
            await SetAuthorizationHeaderAsync(httpClient);

            var queryParams = BuildQueryParams(request);
            var apiUrl = _configuration["ApiSettings:BaseUrl"];
            
            var response = await httpClient.GetAsync($"{apiUrl}/api/statistics/dashboard{queryParams}");
            
            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                var options = new JsonSerializerOptions { PropertyNameCaseInsensitive = true };
                return JsonSerializer.Deserialize<DashboardStatistics>(content, options) ?? new DashboardStatistics();
            }
            
            // Fallback to demo data
            return GetDemoDashboardStatistics();
        }
        catch (Exception)
        {
            // Fallback to demo data on error
            return GetDemoDashboardStatistics();
        }
    }

    public async Task<SalesStatistics> GetSalesStatisticsAsync(StatisticsRequest request)
    {
        try
        {
            using var httpClient = _httpClientFactory.CreateClient();
            await SetAuthorizationHeaderAsync(httpClient);

            var queryParams = BuildQueryParams(request);
            var apiUrl = _configuration["ApiSettings:BaseUrl"];
            
            var response = await httpClient.GetAsync($"{apiUrl}/api/statistics/sales{queryParams}");
            
            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                var options = new JsonSerializerOptions { PropertyNameCaseInsensitive = true };
                return JsonSerializer.Deserialize<SalesStatistics>(content, options) ?? new SalesStatistics();
            }
            
            // Fallback to demo data
            return GetDemoSalesStatistics();
        }
        catch (Exception)
        {
            return GetDemoSalesStatistics();
        }
    }

    public async Task<List<TopProduct>> GetTopProductsAsync(StatisticsRequest request, int limit = 10)
    {
        try
        {
            using var httpClient = _httpClientFactory.CreateClient();
            await SetAuthorizationHeaderAsync(httpClient);

            var queryParams = BuildQueryParams(request);
            queryParams += queryParams.Contains("?") ? "&" : "?";
            queryParams += $"limit={limit}";
            
            var apiUrl = _configuration["ApiSettings:BaseUrl"];
            var response = await httpClient.GetAsync($"{apiUrl}/api/statistics/top-products{queryParams}");
            
            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                var options = new JsonSerializerOptions { PropertyNameCaseInsensitive = true };
                return JsonSerializer.Deserialize<List<TopProduct>>(content, options) ?? new List<TopProduct>();
            }
            
            // Fallback to demo data
            return GetDemoTopProducts();
        }
        catch (Exception)
        {
            return GetDemoTopProducts();
        }
    }

    public async Task<List<RegionSales>> GetSalesByRegionAsync(StatisticsRequest request)
    {
        try
        {
            using var httpClient = _httpClientFactory.CreateClient();
            await SetAuthorizationHeaderAsync(httpClient);

            var queryParams = BuildQueryParams(request);
            var apiUrl = _configuration["ApiSettings:BaseUrl"];
            
            var response = await httpClient.GetAsync($"{apiUrl}/api/statistics/sales-by-region{queryParams}");
            
            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                var options = new JsonSerializerOptions { PropertyNameCaseInsensitive = true };
                return JsonSerializer.Deserialize<List<RegionSales>>(content, options) ?? new List<RegionSales>();
            }
            
            // Fallback to demo data
            return GetDemoRegionSales();
        }
        catch (Exception)
        {
            return GetDemoRegionSales();
        }
    }

    public async Task<List<CategorySales>> GetSalesByCategoryAsync(StatisticsRequest request)
    {
        try
        {
            using var httpClient = _httpClientFactory.CreateClient();
            await SetAuthorizationHeaderAsync(httpClient);

            var queryParams = BuildQueryParams(request);
            var apiUrl = _configuration["ApiSettings:BaseUrl"];
            
            var response = await httpClient.GetAsync($"{apiUrl}/api/statistics/sales-by-category{queryParams}");
            
            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                var options = new JsonSerializerOptions { PropertyNameCaseInsensitive = true };
                return JsonSerializer.Deserialize<List<CategorySales>>(content, options) ?? new List<CategorySales>();
            }
            
            // Fallback to demo data
            return GetDemoCategorySales();
        }
        catch (Exception)
        {
            return GetDemoCategorySales();
        }
    }

    public async Task<List<SalesChartData>> GetSalesChartDataAsync(StatisticsRequest request)
    {
        try
        {
            using var httpClient = _httpClientFactory.CreateClient();
            await SetAuthorizationHeaderAsync(httpClient);

            var queryParams = BuildQueryParams(request);
            var apiUrl = _configuration["ApiSettings:BaseUrl"];
            
            var response = await httpClient.GetAsync($"{apiUrl}/api/statistics/sales-chart{queryParams}");
            
            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                var options = new JsonSerializerOptions { PropertyNameCaseInsensitive = true };
                return JsonSerializer.Deserialize<List<SalesChartData>>(content, options) ?? new List<SalesChartData>();
            }
            
            // Fallback to demo data
            return GetDemoSalesChartData(request);
        }
        catch (Exception)
        {
            return GetDemoSalesChartData(request);
        }
    }

    public async Task<List<SalesReport>> GetSalesReportAsync(StatisticsRequest request)
    {
        try
        {
            using var httpClient = _httpClientFactory.CreateClient();
            await SetAuthorizationHeaderAsync(httpClient);

            var queryParams = BuildQueryParams(request);
            var apiUrl = _configuration["ApiSettings:BaseUrl"];
            
            var response = await httpClient.GetAsync($"{apiUrl}/api/statistics/sales-report{queryParams}");
            
            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                var options = new JsonSerializerOptions { PropertyNameCaseInsensitive = true };
                return JsonSerializer.Deserialize<List<SalesReport>>(content, options) ?? new List<SalesReport>();
            }
            
            // Fallback to demo data
            return GetDemoSalesReport(request);
        }
        catch (Exception)
        {
            return GetDemoSalesReport(request);
        }
    }

    public async Task<byte[]> ExportStatisticsAsync(ExportRequest request)
    {
        try
        {
            using var httpClient = _httpClientFactory.CreateClient();
            await SetAuthorizationHeaderAsync(httpClient);

            var json = JsonSerializer.Serialize(request);
            var content = new StringContent(json, Encoding.UTF8, "application/json");
            
            var apiUrl = _configuration["ApiSettings:BaseUrl"];
            var response = await httpClient.PostAsync($"{apiUrl}/api/statistics/export", content);
            
            if (response.IsSuccessStatusCode)
            {
                return await response.Content.ReadAsByteArrayAsync();
            }
            
            return Array.Empty<byte>();
        }
        catch (Exception)
        {
            return Array.Empty<byte>();
        }
    }

    private async Task SetAuthorizationHeaderAsync(HttpClient httpClient)
    {
        try
        {
            var token = await _jsRuntime.InvokeAsync<string>("localStorage.getItem", "authToken");
            if (!string.IsNullOrEmpty(token))
            {
                httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);
            }
        }
        catch (Exception)
        {
            // Handle JS interop errors silently
        }
    }

    private string BuildQueryParams(StatisticsRequest request)
    {
        var queryParams = new List<string>();
        
        if (request.StartDate.HasValue)
            queryParams.Add($"startDate={request.StartDate.Value:yyyy-MM-dd}");
        if (request.EndDate.HasValue)
            queryParams.Add($"endDate={request.EndDate.Value:yyyy-MM-dd}");
        if (request.SellerId.HasValue)
            queryParams.Add($"sellerId={request.SellerId.Value}");
        if (!string.IsNullOrEmpty(request.Period))
            queryParams.Add($"period={request.Period}");

        return queryParams.Count > 0 ? "?" + string.Join("&", queryParams) : "";
    }

    private DashboardStatistics GetDemoDashboardStatistics()
    {
        return new DashboardStatistics
        {
            SalesStats = GetDemoSalesStatistics(),
            TopProducts = GetDemoTopProducts(),
            SalesByRegion = GetDemoRegionSales(),
            SalesByCategory = GetDemoCategorySales(),
            SalesChartData = GetDemoSalesChartData(new StatisticsRequest { Period = "30" }),
            RecentSales = GetDemoSalesReport(new StatisticsRequest { StartDate = DateTime.Now.AddDays(-7), EndDate = DateTime.Now })
        };
    }

    private SalesStatistics GetDemoSalesStatistics()
    {
        return new SalesStatistics
        {
            TotalSales = 3250000,
            SalesGrowth = 12.5,
            TotalOrders = 45,
            OrdersGrowth = 8.2,
            AverageOrderValue = 72222,
            AovGrowth = 4.3,
            ConversionRate = 3.8,
            ConversionGrowth = -0.5
        };
    }

    private List<TopProduct> GetDemoTopProducts()
    {
        return new List<TopProduct>
        {
            new TopProduct { ProductId = 1, Name = "Smartphone XYZ", UnitsSold = 15, Revenue = 2250000, CategoryName = "Électronique", ImageUrl = "/images/products/smartphone.jpg" },
            new TopProduct { ProductId = 2, Name = "Écouteurs Sans Fil", UnitsSold = 12, Revenue = 300000, CategoryName = "Électronique", ImageUrl = "/images/products/headphones.jpg" },
            new TopProduct { ProductId = 3, Name = "T-shirt Coton", UnitsSold = 25, Revenue = 200000, CategoryName = "Vêtements", ImageUrl = "/images/products/tshirt.jpg" },
            new TopProduct { ProductId = 4, Name = "Tablette 10 pouces", UnitsSold = 5, Revenue = 600000, CategoryName = "Électronique", ImageUrl = "/images/products/tablet.jpg" },
            new TopProduct { ProductId = 5, Name = "Crème Hydratante", UnitsSold = 18, Revenue = 135000, CategoryName = "Beauté", ImageUrl = "/images/products/cream.jpg" }
        };
    }

    private List<RegionSales> GetDemoRegionSales()
    {
        return new List<RegionSales>
        {
            new RegionSales { Name = "Conakry", OrderCount = 15, Revenue = 1200000, Percentage = 36.9 },
            new RegionSales { Name = "Dakar", OrderCount = 12, Revenue = 950000, Percentage = 29.2 },
            new RegionSales { Name = "Bamako", OrderCount = 8, Revenue = 650000, Percentage = 20.0 },
            new RegionSales { Name = "Abidjan", OrderCount = 10, Revenue = 450000, Percentage = 13.9 }
        };
    }

    private List<CategorySales> GetDemoCategorySales()
    {
        return new List<CategorySales>
        {
            new CategorySales { CategoryId = 1, CategoryName = "Électronique", Revenue = 3150000, OrderCount = 32, Percentage = 96.9, Color = "#0d6efd" },
            new CategorySales { CategoryId = 2, CategoryName = "Vêtements", Revenue = 200000, OrderCount = 8, Percentage = 6.2, Color = "#fd7e14" },
            new CategorySales { CategoryId = 3, CategoryName = "Beauté", Revenue = 135000, OrderCount = 5, Percentage = 4.2, Color = "#198754" }
        };
    }

    private List<SalesChartData> GetDemoSalesChartData(StatisticsRequest request)
    {
        var data = new List<SalesChartData>();
        var days = int.Parse(request.Period ?? "30");
        var random = new Random(123);

        for (int i = days; i >= 0; i--)
        {
            var date = DateTime.Now.AddDays(-i);
            var sales = random.Next(50000, 200000);
            var orders = random.Next(1, 8);

            data.Add(new SalesChartData
            {
                Date = date,
                Sales = sales,
                Orders = orders
            });
        }

        return data;
    }

    private List<SalesReport> GetDemoSalesReport(StatisticsRequest request)
    {
        var reports = new List<SalesReport>();
        var startDate = request.StartDate ?? DateTime.Now.AddDays(-7);
        var endDate = request.EndDate ?? DateTime.Now;
        var currentDate = startDate;
        var random = new Random(123);

        while (currentDate <= endDate)
        {
            var orderCount = random.Next(1, 8);
            var productsSold = random.Next(orderCount, orderCount * 3);
            var revenue = productsSold * (random.Next(5000, 20000));
            
            reports.Add(new SalesReport
            {
                Date = currentDate,
                OrderCount = orderCount,
                ProductsSold = productsSold,
                Revenue = revenue,
                AverageOrderValue = orderCount > 0 ? revenue / orderCount : 0
            });
            
            currentDate = currentDate.AddDays(1);
        }

        return reports;
    }
}
