@page "/statistics"

<h1 class="visually-hidden">Statistiques de Vente - NafaPlace</h1>

<div class="container-fluid px-4">
    <h1 class="mt-4">Statistiques de Vente</h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item"><a href="/dashboard">Dashboard</a></li>
        <li class="breadcrumb-item active">Statistiques</li>
    </ol>

    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="text-uppercase">Ventes Totales</h6>
                            <h3>@_totalSales.ToString("N0") GNF</h3>
                        </div>
                        <i class="fas fa-chart-line fa-2x"></i>
                    </div>
                    <div class="mt-2 small">
                        <span class="@(_salesGrowth >= 0 ? "text-success" : "text-danger")">
                            <i class="fas @(_salesGrowth >= 0 ? "fa-arrow-up" : "fa-arrow-down")"></i>
                            @Math.Abs(_salesGrowth).ToString("N1")%
                        </span>
                        <span class="ms-2">depuis le mois dernier</span>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="text-uppercase">Commandes</h6>
                            <h3>@_totalOrders</h3>
                        </div>
                        <i class="fas fa-shopping-cart fa-2x"></i>
                    </div>
                    <div class="mt-2 small">
                        <span class="@(_ordersGrowth >= 0 ? "text-light" : "text-danger")">
                            <i class="fas @(_ordersGrowth >= 0 ? "fa-arrow-up" : "fa-arrow-down")"></i>
                            @Math.Abs(_ordersGrowth).ToString("N1")%
                        </span>
                        <span class="ms-2">depuis le mois dernier</span>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="text-uppercase">Panier Moyen</h6>
                            <h3>@_averageOrderValue.ToString("N0") GNF</h3>
                        </div>
                        <i class="fas fa-coins fa-2x"></i>
                    </div>
                    <div class="mt-2 small">
                        <span class="@(_aovGrowth >= 0 ? "text-light" : "text-danger")">
                            <i class="fas @(_aovGrowth >= 0 ? "fa-arrow-up" : "fa-arrow-down")"></i>
                            @Math.Abs(_aovGrowth).ToString("N1")%
                        </span>
                        <span class="ms-2">depuis le mois dernier</span>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="text-uppercase">Taux de Conversion</h6>
                            <h3>@_conversionRate.ToString("N1")%</h3>
                        </div>
                        <i class="fas fa-percentage fa-2x"></i>
                    </div>
                    <div class="mt-2 small">
                        <span class="@(_conversionGrowth >= 0 ? "text-light" : "text-danger")">
                            <i class="fas @(_conversionGrowth >= 0 ? "fa-arrow-up" : "fa-arrow-down")"></i>
                            @Math.Abs(_conversionGrowth).ToString("N1")%
                        </span>
                        <span class="ms-2">depuis le mois dernier</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-8">
            <div class="card mb-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <div>
                        <i class="fas fa-chart-area me-1"></i>
                        Évolution des Ventes
                    </div>
                    <div>
                        <select class="form-select form-select-sm" @bind="_salesChartPeriod">
                            <option value="7">7 derniers jours</option>
                            <option value="30">30 derniers jours</option>
                            <option value="90">90 derniers jours</option>
                            <option value="365">12 derniers mois</option>
                        </select>
                    </div>
                </div>
                <div class="card-body">
                    <div style="height: 300px; background-color: #f8f9fa; display: flex; align-items: center; justify-content: center;">
                        <div class="text-center">
                            <p class="text-muted mb-2">Graphique des ventes sur la période sélectionnée</p>
                            <div class="chart-placeholder" style="height: 200px; width: 100%; position: relative;">
                                <!-- Simulation d'un graphique avec des barres -->
                                @for (int i = 0; i < 12; i++)
                                {
                                    var height = 20 + (new Random(i * 10).Next(0, 150));
                                    <div style="position: absolute; bottom: 0; left: @(i * 8)%; width: 6%; height: @(height)px; background-color: #0d6efd; opacity: 0.7; border-radius: 3px 3px 0 0;"></div>
                                }
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card mb-4">
                <div class="card-header">
                    <i class="fas fa-chart-pie me-1"></i>
                    Répartition des Ventes par Catégorie
                </div>
                <div class="card-body">
                    <div style="height: 300px; background-color: #f8f9fa; display: flex; align-items: center; justify-content: center;">
                        <div class="text-center">
                            <p class="text-muted mb-3">Répartition par catégorie</p>
                            <div class="chart-placeholder" style="height: 180px; width: 180px; margin: 0 auto; position: relative; border-radius: 50%; overflow: hidden;">
                                <!-- Simulation d'un graphique en camembert -->
                                <div style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; clip-path: polygon(50% 50%, 100% 0, 100% 100%, 0 100%, 0 0); background-color: #0d6efd;"></div>
                                <div style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; clip-path: polygon(50% 50%, 0 0, 100% 0); background-color: #fd7e14;"></div>
                                <div style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; clip-path: polygon(50% 50%, 100% 0, 100% 50%); background-color: #198754;"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-6">
            <div class="card mb-4">
                <div class="card-header">
                    <i class="fas fa-trophy me-1"></i>
                    Produits les Plus Vendus
                </div>
                <div class="card-body">
                    <table class="table table-bordered table-hover">
                        <thead>
                            <tr>
                                <th>Produit</th>
                                <th>Unités Vendues</th>
                                <th>Chiffre d'Affaires</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (var product in _topProducts)
                            {
                                <tr>
                                    <td>@product.Name</td>
                                    <td>@product.UnitsSold</td>
                                    <td>@product.Revenue.ToString("N0") GNF</td>
                                </tr>
                            }
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card mb-4">
                <div class="card-header">
                    <i class="fas fa-map-marker-alt me-1"></i>
                    Ventes par Région
                </div>
                <div class="card-body">
                    <table class="table table-bordered table-hover">
                        <thead>
                            <tr>
                                <th>Région</th>
                                <th>Commandes</th>
                                <th>Chiffre d'Affaires</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (var region in _salesByRegion)
                            {
                                <tr>
                                    <td>@region.Name</td>
                                    <td>@region.OrderCount</td>
                                    <td>@region.Revenue.ToString("N0") XOF</td>
                                </tr>
                            }
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <div class="card mb-4">
        <div class="card-header d-flex justify-content-between align-items-center">
            <div>
                <i class="fas fa-table me-1"></i>
                Rapport de Ventes Détaillé
            </div>
            <div>
                <button class="btn btn-sm btn-outline-primary me-2" @onclick="ExportSalesReport">
                    <i class="fas fa-file-export me-1"></i> Exporter
                </button>
                <button class="btn btn-sm btn-outline-secondary" @onclick="RefreshData">
                    <i class="fas fa-sync me-1"></i> Actualiser
                </button>
            </div>
        </div>
        <div class="card-body">
            <div class="row mb-3">
                <div class="col-md-4">
                    <div class="input-group">
                        <span class="input-group-text">Du</span>
                        <input type="date" class="form-control" @bind="_reportStartDate">
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="input-group">
                        <span class="input-group-text">Au</span>
                        <input type="date" class="form-control" @bind="_reportEndDate">
                    </div>
                </div>
                <div class="col-md-4">
                    <button class="btn btn-primary w-100" @onclick="GenerateReport">
                        <i class="fas fa-search me-1"></i> Générer le Rapport
                    </button>
                </div>
            </div>

            <table class="table table-bordered table-hover">
                <thead>
                    <tr>
                        <th>Date</th>
                        <th>Commandes</th>
                        <th>Produits Vendus</th>
                        <th>Chiffre d'Affaires</th>
                        <th>Panier Moyen</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach (var report in _salesReports)
                    {
                        <tr>
                            <td>@report.Date.ToString("dd/MM/yyyy")</td>
                            <td>@report.OrderCount</td>
                            <td>@report.ProductsSold</td>
                            <td>@report.Revenue.ToString("N0") GNF</td>
                            <td>@report.AverageOrderValue.ToString("N0") GNF</td>
                        </tr>
                    }
                </tbody>
                <tfoot>
                    <tr class="table-primary">
                        <td><strong>Total</strong></td>
                        <td><strong>@_salesReports.Sum(r => r.OrderCount)</strong></td>
                        <td><strong>@_salesReports.Sum(r => r.ProductsSold)</strong></td>
                        <td><strong>@_salesReports.Sum(r => r.Revenue).ToString("N0") GNF</strong></td>
                        <td><strong>@(_salesReports.Any() ? (_salesReports.Sum(r => r.Revenue) / _salesReports.Sum(r => r.OrderCount)).ToString("N0") : "0") GNF</strong></td>
                    </tr>
                </tfoot>
            </table>
        </div>
    </div>
</div>

@code {
    // Statistiques globales
    private decimal _totalSales = 3250000;
    private double _salesGrowth = 12.5;
    private int _totalOrders = 45;
    private double _ordersGrowth = 8.2;
    private decimal _averageOrderValue = 72222;
    private double _aovGrowth = 4.3;
    private double _conversionRate = 3.8;
    private double _conversionGrowth = -0.5;

    // Période pour le graphique des ventes
    private string _salesChartPeriod = "30";

    // Produits les plus vendus
    private List<TopProduct> _topProducts = new List<TopProduct>
    {
        new TopProduct { Name = "Smartphone XYZ", UnitsSold = 15, Revenue = 2250000 },
        new TopProduct { Name = "Écouteurs Sans Fil", UnitsSold = 12, Revenue = 300000 },
        new TopProduct { Name = "T-shirt Coton", UnitsSold = 25, Revenue = 200000 },
        new TopProduct { Name = "Tablette 10 pouces", UnitsSold = 5, Revenue = 600000 },
        new TopProduct { Name = "Crème Hydratante", UnitsSold = 18, Revenue = 135000 }
    };

    // Ventes par région
    private List<RegionSales> _salesByRegion = new List<RegionSales>
    {
        new RegionSales { Name = "Conakry", OrderCount = 15, Revenue = 1200000 },
        new RegionSales { Name = "Dakar", OrderCount = 12, Revenue = 950000 },
        new RegionSales { Name = "Bamako", OrderCount = 8, Revenue = 650000 },
        new RegionSales { Name = "Abidjan", OrderCount = 10, Revenue = 450000 }
    };

    // Rapport de ventes détaillé
    private DateTime _reportStartDate = DateTime.Now.AddDays(-7);
    private DateTime _reportEndDate = DateTime.Now;
    private List<SalesReport> _salesReports = new List<SalesReport>();

    protected override void OnInitialized()
    {
        GenerateReport();
    }

    private void GenerateReport()
    {
        _salesReports.Clear();
        var currentDate = _reportStartDate;
        var random = new Random(123);

        while (currentDate <= _reportEndDate)
        {
            var orderCount = random.Next(1, 8);
            var productsSold = random.Next(orderCount, orderCount * 3);
            var revenue = productsSold * (random.Next(5000, 20000));
            
            _salesReports.Add(new SalesReport
            {
                Date = currentDate,
                OrderCount = orderCount,
                ProductsSold = productsSold,
                Revenue = revenue,
                AverageOrderValue = orderCount > 0 ? revenue / orderCount : 0
            });
            
            currentDate = currentDate.AddDays(1);
        }
    }

    private void ExportSalesReport()
    {
        // Implémenter l'exportation du rapport
        Console.WriteLine("Exporting sales report...");
    }

    private void RefreshData()
    {
        // Simuler une actualisation des données
        GenerateReport();
    }

    public class TopProduct
    {
        public string Name { get; set; } = "";
        public int UnitsSold { get; set; }
        public decimal Revenue { get; set; }
    }

    public class RegionSales
    {
        public string Name { get; set; } = "";
        public int OrderCount { get; set; }
        public decimal Revenue { get; set; }
    }

    public class SalesReport
    {
        public DateTime Date { get; set; }
        public int OrderCount { get; set; }
        public int ProductsSold { get; set; }
        public decimal Revenue { get; set; }
        public decimal AverageOrderValue { get; set; }
    }
}
